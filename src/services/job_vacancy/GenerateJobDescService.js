const AppService = require('../AppService');
const { InternalJobProfileData } = require('../../models');
const { UserAssessmentResult } = require('../../models');

class GenerateJobDescService extends AppService {
  constructor({ onetService, googleAiService, qdrantClient }) {
    super();
    this.onetService = onetService;
    this.googleAiService = googleAiService;
    this.qdrantClient = qdrantClient;
  }

  /**
   * Generate job description based on job title name
   * @param {string} name - Job title name
   * @param {Array} topUserIds - Array of top user ids
   * @returns {Promise<Object>} Job description object
   */
  async generateJobDesc(name = '', topUserIds = [], jobLevelName = '') {
    const relatedJobTitles = await this.getRelatedJobTitles(name);
    const allJobTitles = [name, ...relatedJobTitles];

    const vectorizedJobTitles = await this.generateVectorTitles(allJobTitles);

    const [onetResults, internalResults] = await Promise.all([
      this.qdrantSearchOnet(vectorizedJobTitles),
      this.qdrantSearchInternal(vectorizedJobTitles),
    ]);

    const searchResults = {
      onetResults,
      internalResults,
    };

    const onetCodes = searchResults.onetResults.map(item => item.onetsoc_code);

    const contextData = await this.processContextData(searchResults, topUserIds);
    const jobDescription = await this.generateJobDescriptionWithAI(name, contextData, jobLevelName);

    return {
      jobTitle: name,
      jobDescription,
      onetsocCodes: onetCodes,
    };
  }

  /**
   * Get 3 related job titles using Gemini API
   * @param {string} jobTitle - Original job title
   * @returns {Array} Array of 3 related job titles
   */
  async getRelatedJobTitles(jobTitle) {
    const systemPrompt = `
        You are an expert HR professional. Given a job title, 
        provide 3 closely related job titles that share similar skills, responsibilities, or career paths. 
        Return only a JSON array of strings with exactly 3 job titles.
    `;

    const userPrompt = `Job Title: ${jobTitle}\n\nProvide 3 closely related job titles:`;

    try {
      const response = await this.googleAiService.generateContent({
        model: 'gemini-2.5-flash',
        contents: [{ role: 'user', parts: [{ text: userPrompt }] }],
        config: {
          temperature: 0,
          responseMimeType: 'application/json',
          systemInstruction: [{ text: systemPrompt }],
        },
      });

      const relatedTitles = JSON.parse(response.candidates[0].content.parts[0].text);
      return relatedTitles.slice(0, 3);
    } catch (error) {
      console.error('Error getting related job titles:', error);
      // Fallback to mock data
      return ['Senior Software Engineer', 'Full Stack Developer'];
    }
  }

  /**
   * Generate vector embeddings for job titles
   * @param {Array} jobTitles - Array of job titles
   * @returns {Array} Array of objects with jobTitle and vector
   */
  async generateVectorTitles(jobTitles) {
    const responses = await this.googleAiService.embedContent({
      model: 'gemini-embedding-001',
      contents: jobTitles,
    });

    const results = responses.embeddings.map((embedding, index) => ({
      jobTitle: jobTitles[index],
      vector: embedding.values,
    }));

    return results;
  }

  /**
   * Search Qdrant for ONET collection data
   * @param {Array} jobTitles - Job title to search
   * @returns {Array} Array of ONET results with onetsoc_code
   */
  async qdrantSearchOnet(jobTitles) {
    const collectionName = 'onet_job_title';

    const searches = jobTitles.map(({ _, vector }) => ({
      vector,
      limit: 3,
    }));

    const searchResult = await this.qdrantClient.searchBatch(collectionName, {
      searches,
    });

    const searchOnetResults = searchResult.flatMap(item => item.flatMap(result => result.id));

    const onetData = await this.qdrantClient.retrieve(collectionName, {
      ids: searchOnetResults,
    });

    const retriveData = onetData.map(item => ({
      onetsoc_code: item.payload.onetsoc_code,
      title: item.payload.job_title,
    }));

    return retriveData;
  }

  /**
   * Search Qdrant for internal collection data
   * @param {Array} jobTitles - Job title to search
   * @returns {Array} Array of internal results with table_internal_id
   */
  async qdrantSearchInternal(jobTitles) {
    const collectionName = 'internal_job_title';

    const searches = jobTitles.map(({ _, vector }) => ({
      vector,
      limit: 3,
    }));

    const searchResult = await this.qdrantClient.searchBatch(collectionName, {
      searches,
    });

    const qdrant_ids = searchResult.flatMap(item => item.flatMap(result => result.id));

    const internalData = await this.qdrantClient.retrieve(collectionName, {
      ids: qdrant_ids,
    });

    const retriveData = internalData.map(item => ({
      table_internal_id: item.payload.table_internal_id,
      title: item.payload.job_title,
    }));

    return retriveData;
  }

  /**
   * Process context data from ONET search results
   * @param {Array} onetResults - Array of ONET search results
   * @returns {Object} Processed ONET context data
   */
  async onetContextData(onetResults) {
    const onetsocCodes = onetResults.map(result => result.onetsoc_code);

    if (onetsocCodes.length === 0) {
      return {
        onetData: 'No onet data available',
        source: 'onet',
      };
    }

    const resultOccupations = await this.onetService.getOccupations(onetsocCodes);
    const resultTasks = await this.onetService.getTasks(onetsocCodes);

    const occupations = Object.values(resultOccupations)
      .map(result => `Job Title: ${result.title}\nJob Description: ${result.description}\n`)
      .join('\n\n');

    const tasks = resultTasks
      .map(
        result =>
          `Task: ${result.task}\nTask Type: ${result.task_type}\n Importance: ${result.importance}`,
      )
      .join('\n\n');

    return {
      occupations,
      tasks,
      source: 'onet',
    };
  }

  /**
   * Process context data from internal search results
   * @param {Array} internalResults - Array of internal search results
   * @returns {Object} Processed internal context data
   */
  async internalContextData(internalResults) {
    const internalIds = internalResults.map(result => result.table_internal_id);

    if (internalIds.length === 0) {
      return {
        internalData: '',
        source: 'internal',
      };
    }

    const results = await InternalJobProfileData.findAll({ where: { id: internalIds } });

    const internalData = results
      .map(
        result =>
          `Job Title: ${result.position_name}\nJob Description: ${result.main_responsibilities}\n`,
      )
      .join('\n\n');

    return {
      internalData,
      source: 'internal',
    };
  }

  /**
   * Process context data from user assessment results
   * @param {Array} userIds - Array of user IDs
   * @returns {Object} Processed user assessment data
   */
  async userAssessmentData(userIds) {
    if (!userIds || userIds.length === 0) {
      return {
        userAssessmentData: 'No user assessment data available',
        source: 'user_assessment',
      };
    }

    const userAssessmentResults = await UserAssessmentResult.findAll({
      where: {
        user_id: userIds,
      },
    });

    // group assesment data by user_ids and assessment
    const groupedAssessmentData = userAssessmentResults.reduce((acc, cur) => {
      if (!acc[cur.user_id]) {
        acc[cur.user_id] = {};
      }
      acc[cur.user_id][cur.assessment] = cur;
      return acc;
    }, {});

    // format user assessment data
    const userAssessmentData = Object.entries(groupedAssessmentData).map(
      ([userId, data]) =>
        `
      User ID: ${userId}
      ${Object.entries(data)
        .map(
          ([assessment, result]) =>
            `
        Assessment: ${assessment}
        Aspect Name: ${result.aspect_name}
        Value Type: ${result.value_type}
        Value: ${result.value}
        `,
        )
        .join('\n')}
      `,
    );

    return {
      userAssessmentData,
      source: 'user_assessment',
    };
  }

  /**
   * Process all context data from search results
   * @param {Array} searchResults - Combined search results
   * @param {Array} topUserIds - Array of top user IDs
   * @returns {Object} Processed context data
   */
  async processContextData(searchResults, topUserIds) {
    const [onetContext, internalContext, userAssessmentContext] = await Promise.all([
      this.onetContextData(searchResults.onetResults),
      this.internalContextData(searchResults.internalResults),
      this.userAssessmentData(topUserIds),
    ]);

    return {
      onetContext,
      internalContext,
      userAssessmentContext,
    };
  }

  /**
   * Generate job description using AI with context data
   * @param {string} jobTitle - Original job title
   * @param {Array} contextData - Processed context data
   * @returns {Object} Generated job description
   */
  async generateJobDescriptionWithAI(jobTitle, contextData, jobLevelName) {
    try {
      const [systemPrompt, userPrompt] = await Promise.all([
        this.getSystemPrompt(jobTitle, contextData, jobLevelName),
        this.getUserPrompt(jobTitle, jobLevelName),
      ]);

      const response = await this.googleAiService.generateContent({
        model: 'gemini-2.5-pro',
        contents: [{ role: 'user', parts: [{ text: userPrompt }] }],
        config: {
          temperature: 0.4,
          responseMimeType: 'application/json',
          systemInstruction: [{ text: systemPrompt }],
        },
      });

      const jobDescription = JSON.parse(response.candidates[0].content.parts[0].text);
      return jobDescription;
    } catch (error) {
      console.error('Error generating job description:', error);
      return {
        key_responsibilities: [],
        qualifications: [],
        competencies: [],
        sucess_metrics: [],
      };
    }
  }

  /**
   * Get system prompt for job description generation
   * @param {string} jobTitle - Job title
   * @param {Array} contextData - Context data from searches
   * @returns {string} System prompt
   */
  async getSystemPrompt(jobTitle, contextData, jobLevelName) {
    const jobLevelText = jobLevelName ? ` for a ${jobLevelName} level position` : '';
    return Promise.resolve(`
      You are an expert on domain of 'TARGET_JOB_TITLE' tasked with creating a clear, comprehensive, and compelling job description${jobLevelText} for a new vacancy on your team. Your goal is to attract top talent by accurately representing the role and its responsibilities.

      **Objective:**
      Analyze the provided contextual data—including internal job profiles, O*Net data, and task lists—to synthesize and generate a detailed job description for the specified 'TARGET_JOB_TITLE'.

      **TASK:**
      1.  Carefully review all sections of the 'CONTEXTUAL_DATA'.
      2.  Analyze the 'TOP_EMPLOYEE_RELATED_DATA' to understand the unique needs, skills, and preferences of top employees in the field.
      3.  Synthesize this information to generate the following distinct components:
          *   **key_responsibilities:** A coherent list of core responsibilities and daily tasks. This section must follow the **"Job Description Writing Rules"** detailed below.
          *   **qualifications:** The necessary background, experience, and credentials that legitimize a candidate's ability to perform the role. This includes minimum years of experience, required educational degrees, and essential certifications.
          *   **competencies:** The key skills and behavioral attributes needed for success. This should be a mix of hard skills (e.g., specific software proficiency, technical abilities) and soft skills (e.g., communication, teamwork, problem-solving).
          *   **sucess_metrics:** The key performance indicators (KPIs) or measurable outcomes that define success in this role. These should be framed as specific, quantifiable achievements (e.g., "Achieve X% growth," "Reduce costs by Y," "Maintain Z satisfaction score").
      4.  Translate any relevant non-English information into fluent, professional English.
      5.  Format your final output strictly according to the defined JSON schema.
      6.  Ensure all generated content is clear, concise, and engaging.

      **Job Description (key_responsibilities) Writing Rules:**
      *   **Structure:** Follow the template: **Verb -> Object/Deliverable -> Scope/Context -> Collaboration/Stakeholder (optional) -> Cadence/Metric (optional).**
      *   **Active Verbs:** Start every bullet point with a strong, active verb in the present tense.
      *   **One Action per Bullet:** Each responsibility must be clear, specific, and contain only one primary action. Avoid using "and" to chain multiple actions.
      *   **Tone:** Be direct, concrete, and outcome-oriented. Avoid vague phrasing like "help" or "support."
      *   **Themes & Verbs:** Distribute responsibilities across these recurring themes, using verbs from the associated bank:
          *   **Execute/Operate:** run, manage, operate, handle, ensure
          *   **Collaborate/Communicate:** coordinate, liaise, collaborate, facilitate, communicate
          *   **Plan/Design:** plan, define, design, outline, scope
          *   **Monitor/Improve:** monitor, analyze, evaluate, audit, optimize
          *   **Build/Implement:** develop, build, implement, integrate, release
          *   **Lead/People:** lead, own, oversee, mentor, coach
          *   **Governance/Compliance:** document, standardize, enforce, align

      **CRITICAL RULES:**
      -   **JSON Output Only:** Your entire response must be a single, valid JSON object, with no introductory text or explanations.
      -   **Relevance is Key:** Every point generated must be directly relevant to the 'TARGET_JOB_TITLE'.
      -   **Language:** The output must be in English.
      -   **Quantity:** Generate a comprehensive list of **15-20** \`key_responsibilities\`, **5-7** \`qualifications\`, **5-7** \`competencies\`, and **3-5** \`sucess_metrics\`.
      -   **Professionalism:** Ensure the language is inclusive, professional, and free of any offensive terms.

      **JSON OUTPUT SCHEMA:**
      {
          "key_responsibilities": String[],
          "qualifications": String[],
          "competencies": String[],
          "sucess_metrics": String[]
      }

      **EXAMPLE OUTPUT:**
      {
          "key_responsibilities": [
              "Manage deployment pipeline for core services in staging with the Engineering team per release.",
              "Coordinate cross-functional readiness with Product and Ops for quarterly releases.",
              "Plan data model changes for analytics in the data warehouse per sprint.",
              "Monitor API latency dashboards and optimize p95 response time to agreed SLA.",
              "Develop and implement background workers for job processing in production.",
              "Document SOPs for incident response and standardize runbooks across teams."
          ],
          "qualifications": [
              "Bachelor's degree in Computer Science, Engineering, or a related technical field.",
              "Minimum of 4 years of professional experience in backend software development.",
              "Proven experience with at least one major cloud platform (e.g., AWS, Azure, GCP).",
              "Demonstrable experience in designing and building RESTful APIs.",
              "Experience with relational and NoSQL databases."
          ],
          "competencies": [
              "Expert proficiency in one or more backend programming languages such as Python, Java, or Go.",
              "Strong analytical and problem-solving skills.",
              "Excellent verbal and written communication abilities.",
              "Ability to work effectively in a collaborative, agile team environment.",
              "Proficiency with version control systems, such as Git."
          ],
          "sucess_metrics": [
              "Maintain a system uptime of 99.95% or higher for all managed services.",
              "Reduce average API response time by 20% over the first year.",
              "Successfully lead the development and launch of at least two major product features per year.",
              "Contribute to a 15% reduction in critical production bugs through improved testing and code quality."
          ]
      }
      ### **INPUT DATA**

      **1. TARGET_JOB_TITLE:**
      ${jobTitle}

      **2. CONTEXTUAL_DATA:**

      **2.1. Internal Company Job Profiles:**
      ${Array.isArray(contextData) ? contextData.map(item => item.internalContext).join('\n') : contextData.internalContext}

      **2.2. O*Net Job Descriptions:**
      ${Array.isArray(contextData) ? contextData.map(item => item.onetContext).join('\n') : contextData.onetContext}

      **2.3. O*Net Related Tasks:**
      Task contains: Task Description, Task Type, and Importance 1-100 (higher importance means more important)
      ${Array.isArray(contextData) ? contextData.map(item => item.tasks || '').join('\n') : contextData.tasks || ''}

      **3. Top Employee Related Data**
      ${Array.isArray(contextData) ? contextData.map(item => item.userAssessmentContext).join('\n') : contextData.userAssessmentContext}
    `);
  }

  /**
   * Generate user prompt with job title and context data
   * @param {string} jobTitle - Job title
   * @returns {string} User prompt
   */
  async getUserPrompt(jobTitle) {
    const userPrompt = `# Job Title\n${jobTitle}\n\n`;
    return Promise.resolve(userPrompt);
  }
}

module.exports = GenerateJobDescService;
