const api = require('../utils/requestHelper');
const { describeWithTransaction } = require('../utils/describeWithTransaction');
const {
  UserFactory,
  JobTitleFactory,
  JobVacancyFactory,
  JobLevelFactory,
} = require('../factories');
const { JobTitle, JobVacancy } = require('../../src/models');
const MockUtils = require('../utils/mockUtils');

describeWithTransaction('JobVacanciesController', () => {
  let admin;
  let user;

  beforeEach(async () => {
    admin = await UserFactory.create({ role: 'admin' });
    user = await UserFactory.create();
  });

  afterEach(() => {
    MockUtils.resetAllMocks();
  });

  describe('GET /api/v1/job_vacancies', () => {
    beforeEach(async () => {
      const jobTitle = await JobTitleFactory.create();
      await JobVacancyFactory.create({ job_title_id: jobTitle.id });
    });

    describe('authentication and authorization', () => {
      it('should return 401 without token', async () => {
        const response = await api.get('/api/v1/job_vacancies');

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Access token required');
      });

      it('should return 403 for non-admin users', async () => {
        const response = await api.as(user).get('/api/v1/job_vacancies');

        expect(response.status).toBe(403);
        expect(response.body).toHaveProperty('error', 'Insufficient permissions');
      });

      it('should return 200 for admin users', async () => {
        const response = await api.as(admin).get('/api/v1/job_vacancies');

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(Array.isArray(response.body.data)).toBe(true);
      });
    });

    describe('successful responses', () => {
      it('should return all job vacancies with correct format', async () => {
        const response = await api.as(admin).get('/api/v1/job_vacancies');

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('pagination');
        expect(Array.isArray(response.body.data)).toBe(true);
        expect(response.body.data).toHaveLength(1);

        // Check the structure of job vacancy objects
        const jobVacancy = response.body.data[0];
        expect(jobVacancy).toHaveProperty('id');
        expect(jobVacancy).toHaveProperty('name');
      });

      it('should include pagination information', async () => {
        const response = await api.as(admin).get('/api/v1/job_vacancies');

        expect(response.body.pagination).toHaveProperty('page');
        expect(response.body.pagination).toHaveProperty('limit');
        expect(response.body.pagination).toHaveProperty('total');
        expect(response.body.pagination.total).toBe(1);
      });
    });

    describe('pagination and filtering', () => {
      beforeEach(async () => {
        const jobTitle = await JobTitle.findOne();
        await JobVacancyFactory.createMany(2, { job_title_id: jobTitle.id });
      });

      it('should support pagination', async () => {
        const params = { page: 1, limit: 2 };
        const response = await api.as(admin).get('/api/v1/job_vacancies', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(2);
        expect(response.body.pagination.page).toBe(1);
        expect(response.body.pagination.limit).toBe(2);
        expect(response.body.pagination.total).toBe(3);
      });

      it('should support search filtering', async () => {
        const vc = await JobVacancy.findOne();
        await vc.update({ name: 'Vibe Coder' });

        const params = { search: 'Vibe' };
        const response = await api.as(admin).get('/api/v1/job_vacancies', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].name).toBe('Vibe Coder');
      });

      it('should support sorting', async () => {
        const lastThree = await JobVacancy.findAll({ order: [['name', 'DESC']], limit: 3 });
        await lastThree[0].update({ name: 'Vibe Coder' });
        await lastThree[1].update({ name: 'Software Engineer' });
        await lastThree[2].update({ name: 'Product Engineer' });

        const params = { sort: 'name', sort_direction: 'desc' };
        const response = await api.as(admin).get('/api/v1/job_vacancies', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(3);
        expect(response.body.data[0].name).toBe('Vibe Coder');
        expect(response.body.data[1].name).toBe('Software Engineer');
        expect(response.body.data[2].name).toBe('Product Engineer');
      });
    });

    describe('empty results', () => {
      it('should return empty array when no job vacancies exist', async () => {
        await JobVacancy.destroy({ where: {}, truncate: true, cascade: true });
        const response = await api.as(admin).get('/api/v1/job_vacancies');

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
        expect(response.body.pagination.total).toBe(0);
      });

      it('should return empty array when filter matches nothing', async () => {
        const params = { search: 'NonexistentTitle' };
        const response = await api.as(admin).get('/api/v1/job_vacancies', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
      });
    });
  });

  describe('GET /api/v1/job_vacancies/:id', () => {
    let vacancy;

    beforeEach(async () => {
      const jobTitle = await JobTitleFactory.create();
      vacancy = await JobVacancyFactory.create({ job_title_id: jobTitle.id });
    });

    it('should return job vacancy for valid ID', async () => {
      const response = await api.as(admin).get(`/api/v1/job_vacancies/${vacancy.id}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('id', vacancy.id);
    });

    it('should return 404 for non-existent job vacancy', async () => {
      const response = await api.as(admin).get('/api/v1/job_vacancies/0');

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('Job vacancy not found');
    });

    it('should return 401 without authentication', async () => {
      const response = await api.get(`/api/v1/job_vacancies/${vacancy.id}`);

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });

    it('should return 403 for non-admin users', async () => {
      const response = await api.as(user).get(`/api/v1/job_vacancies/${vacancy.id}`);

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('POST /api/v1/job_vacancies', () => {
    let jobTitle;
    let jobLevel;
    let validCreateData;

    beforeEach(async () => {
      jobTitle = await JobTitleFactory.create();
      jobLevel = await JobLevelFactory.create();

      validCreateData = {
        name: 'Senior Software Engineer',
        job_title_id: jobTitle.id,
        job_level_id: jobLevel.id,
        job_description: 'Develop and maintain software applications',
        reference_user_ids: [user.id],
        detailed_descriptions: {
          key_responsibilities: ['Lead software development projects', 'Mentor junior developers'],
          qualifications: ["Bachelor's degree in Computer Science", '5+ years of experience'],
          competencies: ['JavaScript', 'React', 'Node.js'],
          success_metrics: ['Deliver projects on time', 'Maintain code quality'],
        },
      };
    });

    describe('authentication and authorization', () => {
      it('should return 401 without token', async () => {
        const response = await api.post('/api/v1/job_vacancies', validCreateData);

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Access token required');
      });

      it('should return 403 for non-admin users', async () => {
        const response = await api.as(user).post('/api/v1/job_vacancies', validCreateData);

        expect(response.status).toBe(403);
        expect(response.body).toHaveProperty('error', 'Insufficient permissions');
      });
    });

    describe('successful creation', () => {
      it('should create job vacancy with valid data', async () => {
        const response = await api.as(admin).post('/api/v1/job_vacancies', validCreateData);

        expect(response.status).toBe(201);
        expect(response.body).toHaveProperty('data');
        expect(response.body.data).toHaveProperty('id');
        expect(response.body.data).toHaveProperty('name', validCreateData.name);
        expect(response.body.data).toHaveProperty('status', 'generating_jobdesc');

        // Verify the job vacancy was created in the database
        const createdVacancy = await JobVacancy.findByPk(response.body.data.id);
        expect(createdVacancy).toBeTruthy();
        expect(createdVacancy.name).toBe(validCreateData.name);
      });

      it('should create job vacancy with minimal data', async () => {
        const minimalData = {
          name: 'Basic Job Position',
        };

        const response = await api.as(admin).post('/api/v1/job_vacancies', minimalData);

        expect(response.status).toBe(201);
        expect(response.body.data).toHaveProperty('name', minimalData.name);
        expect(response.body.data).toHaveProperty('status', 'generating_jobdesc');
      });

      it('should handle detailed_descriptions correctly', async () => {
        const response = await api.as(admin).post('/api/v1/job_vacancies', validCreateData);

        expect(response.status).toBe(201);
        expect(response.body.data).toHaveProperty('detailed_descriptions');
        expect(response.body.data.detailed_descriptions).toHaveProperty('key_responsibilities');
        expect(response.body.data.detailed_descriptions.key_responsibilities).toEqual(
          validCreateData.detailed_descriptions.key_responsibilities,
        );
      });
    });

    describe('validation errors', () => {
      it('should return 400 for invalid job_title_id', async () => {
        const invalidData = {
          ...validCreateData,
          job_title_id: 'invalid',
        };

        const response = await api.as(admin).post('/api/v1/job_vacancies', invalidData);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 for invalid job_level_id', async () => {
        const invalidData = {
          ...validCreateData,
          job_level_id: 'invalid',
        };

        const response = await api.as(admin).post('/api/v1/job_vacancies', invalidData);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 for invalid reference_user_ids', async () => {
        const invalidData = {
          ...validCreateData,
          reference_user_ids: ['invalid', 'ids'],
        };

        const response = await api.as(admin).post('/api/v1/job_vacancies', invalidData);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should ignore additional properties', async () => {
        const dataWithAdditionalProps = {
          ...validCreateData,
          invalid_field: 'should be ignored',
        };

        const response = await api.as(admin).post('/api/v1/job_vacancies', dataWithAdditionalProps);

        expect(response.status).toBe(201);
        expect(response.body.data).toHaveProperty('name', validCreateData.name);
        expect(response.body.data).not.toHaveProperty('invalid_field');
      });
    });

    describe('edge cases', () => {
      it('should return 400 for empty request body (missing required name)', async () => {
        const response = await api.as(admin).post('/api/v1/job_vacancies', {});

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error');
      });

      it('should handle null values in optional fields', async () => {
        const dataWithNulls = {
          name: 'Test Position',
          job_description: null,
        };

        const response = await api.as(admin).post('/api/v1/job_vacancies', dataWithNulls);

        expect(response.status).toBe(201);
        expect(response.body.data).toHaveProperty('name', dataWithNulls.name);
      });
    });
  });

  describe('PATCH /api/v1/job_vacancies/:id', () => {
    let jobTitle;
    let jobLevel;
    let existingVacancy;
    let validUpdateData;

    beforeEach(async () => {
      jobTitle = await JobTitleFactory.create();
      jobLevel = await JobLevelFactory.create();
      existingVacancy = await JobVacancyFactory.create({
        job_title_id: jobTitle.id,
        job_level_id: jobLevel.id,
      });

      validUpdateData = {
        name: 'Updated Senior Software Engineer',
        job_desc: [
          'Develop scalable web applications',
          'Lead technical architecture decisions',
          'Mentor team members',
        ],
        reference_user_ids: [user.id],
        detailed_descriptions: {
          key_responsibilities: ['Lead complex software projects', 'Design system architecture'],
          qualifications: ["Master's degree preferred", '7+ years of experience'],
        },
        job_level_id: jobLevel.id,
      };
    });

    describe('authentication and authorization', () => {
      it('should return 401 without token', async () => {
        const response = await api.patch(
          `/api/v1/job_vacancies/${existingVacancy.id}`,
          validUpdateData,
        );

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Access token required');
      });

      it('should return 403 for non-admin users', async () => {
        const response = await api
          .as(user)
          .patch(`/api/v1/job_vacancies/${existingVacancy.id}`, validUpdateData);

        expect(response.status).toBe(403);
        expect(response.body).toHaveProperty('error', 'Insufficient permissions');
      });
    });

    describe('successful updates', () => {
      it('should update job vacancy with valid data', async () => {
        const response = await api
          .as(admin)
          .patch(`/api/v1/job_vacancies/${existingVacancy.id}`, validUpdateData);

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(response.body.data).toHaveProperty('id', existingVacancy.id);
        expect(response.body.data).toHaveProperty('name', validUpdateData.name);
        expect(response.body.data).toHaveProperty('job_desc', validUpdateData.job_desc);

        // Verify the job vacancy was updated in the database
        const updatedVacancy = await JobVacancy.findByPk(existingVacancy.id);
        expect(updatedVacancy.name).toBe(validUpdateData.name);
        expect(updatedVacancy.job_desc).toEqual(validUpdateData.job_desc);
      });

      it('should update only provided fields', async () => {
        const partialUpdate = {
          name: 'Partially Updated Position',
        };

        const response = await api
          .as(admin)
          .patch(`/api/v1/job_vacancies/${existingVacancy.id}`, partialUpdate);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveProperty('name', partialUpdate.name);

        // Verify other fields remain unchanged
        const updatedVacancy = await JobVacancy.findByPk(existingVacancy.id);
        expect(updatedVacancy.name).toBe(partialUpdate.name);
      });

      it('should handle detailed_descriptions updates', async () => {
        const updateWithDescriptions = {
          detailed_descriptions: {
            key_responsibilities: ['New responsibility 1', 'New responsibility 2'],
            qualifications: ['New qualification 1'],
          },
        };

        const response = await api
          .as(admin)
          .patch(`/api/v1/job_vacancies/${existingVacancy.id}`, updateWithDescriptions);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveProperty('detailed_descriptions');
        expect(response.body.data.detailed_descriptions).toMatchObject(
          updateWithDescriptions.detailed_descriptions,
        );
      });
    });

    describe('followup actions with external services', () => {
      it('should handle generate_jobdesc followup action', async () => {
        // First update the vacancy to draft status so it can accept followup actions
        await existingVacancy.update({ status: 'draft' });

        const updateWithFollowup = {
          name: 'Position for Job Desc Generation',
          followup_action: 'generate_jobdesc',
        };

        const response = await api
          .as(admin)
          .patch(`/api/v1/job_vacancies/${existingVacancy.id}`, updateWithFollowup);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveProperty('name', updateWithFollowup.name);
        expect(response.body.data).toHaveProperty('status', 'generating_jobdesc');

        // Note: The actual job description generation happens asynchronously
        // so we can't test the final result here, but we can verify the request was successful
      });

      it('should handle generate_job_variables followup action', async () => {
        await existingVacancy.update({ status: 'draft' });

        const updateWithFollowup = {
          name: 'Position for Variable Generation',
          followup_action: 'generate_job_variables',
        };

        const response = await api
          .as(admin)
          .patch(`/api/v1/job_vacancies/${existingVacancy.id}`, updateWithFollowup);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveProperty('name', updateWithFollowup.name);
        expect(response.body.data).toHaveProperty('status', 'generating_job_variables');
      });

      it('should reject followup actions for non-draft vacancies', async () => {
        // Ensure the vacancy is not in draft status
        await existingVacancy.update({ status: 'active' });

        const updateWithFollowup = {
          name: 'Position with Invalid Status',
          followup_action: 'generate_jobdesc',
        };

        const response = await api
          .as(admin)
          .patch(`/api/v1/job_vacancies/${existingVacancy.id}`, updateWithFollowup);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error');
      });
    });

    describe('validation errors', () => {
      it('should return 400 for invalid followup_action', async () => {
        const invalidData = {
          followup_action: 'invalid_action',
        };

        const response = await api
          .as(admin)
          .patch(`/api/v1/job_vacancies/${existingVacancy.id}`, invalidData);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 for invalid reference_user_ids', async () => {
        const invalidData = {
          reference_user_ids: ['invalid', 'ids'],
        };

        const response = await api
          .as(admin)
          .patch(`/api/v1/job_vacancies/${existingVacancy.id}`, invalidData);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should ignore additional properties', async () => {
        const dataWithAdditionalProps = {
          name: 'Updated Position',
          invalid_field: 'should be ignored',
        };

        const response = await api
          .as(admin)
          .patch(`/api/v1/job_vacancies/${existingVacancy.id}`, dataWithAdditionalProps);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveProperty('name', 'Updated Position');
        expect(response.body.data).not.toHaveProperty('invalid_field');
      });
    });

    describe('error cases', () => {
      it('should return 404 for non-existent job vacancy', async () => {
        const response = await api.as(admin).patch('/api/v1/job_vacancies/99999', validUpdateData);

        expect(response.status).toBe(404);
        expect(response.body).toHaveProperty('error');
      });

      it('should handle empty request body', async () => {
        const response = await api
          .as(admin)
          .patch(`/api/v1/job_vacancies/${existingVacancy.id}`, {});

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveProperty('id', existingVacancy.id);
      });
    });
  });
});
