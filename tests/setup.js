require('dotenv').config({ path: '.env.test', quiet: true });

// Set test environment
process.env.NODE_ENV = 'test';

const { sequelize } = require('../src/models');
const QdrantServiceMock = require('./mocks/QdrantServiceMock');
const GoogleAiServiceMock = require('./mocks/GoogleAiServiceMock');

// Mock Qdrant client globally to avoid connection failures
jest.mock('@qdrant/js-client-rest', () => {
  return {
    QdrantClient: jest.fn().mockImplementation(() => new QdrantServiceMock()),
  };
});

// Mock Google AI service globally to avoid expensive API calls
jest.mock('@google/genai', () => {
  return {
    GoogleGenAI: jest.fn().mockImplementation(() => new GoogleAiServiceMock()),
  };
});

// Mock VacancyGroupVariableService to avoid complex SQL operations during tests
jest.mock('../src/services/VacancyGroupVariableService', () => {
  const originalModule = jest.requireActual('../src/services/VacancyGroupVariableService');

  return class VacancyGroupVariableService extends originalModule {
    async calculateUserVariableConstants(_jobVacancyId) {
      // Mock implementation that doesn't execute complex SQL
      return Promise.resolve();
    }
  };
});

// Mock OnetService to avoid database queries to non-existent onet_occupations table
jest.mock('../src/services/OnetService', () => {
  return class OnetService {
    async getOccupations(_onetsocCodes) {
      // Mock implementation that returns sample occupation data
      return [
        {
          onetsoc_code: '15-1252.00',
          title: 'Software Developers, Applications',
          description:
            'Develop, create, and modify general computer applications software or specialized utility programs.',
        },
        {
          onetsoc_code: '15-1251.00',
          title: 'Computer Programmers',
          description:
            'Create, modify, and test the code and scripts that allow computer applications to run.',
        },
      ];
    }

    async getTasks(_onetsocCodes) {
      // Mock implementation that returns sample task data
      return [
        {
          onetsoc_code: '15-1252.00',
          task: 'Analyze user requirements to derive technical software design and performance requirements.',
        },
        {
          onetsoc_code: '15-1252.00',
          task: 'Debug, maintain, and update existing software applications.',
        },
        {
          onetsoc_code: '15-1251.00',
          task: 'Write, update, and maintain computer programs or software packages.',
        },
      ];
    }
  };
});

// Mock job description generation service to avoid external API calls
jest.mock('../src/services/job_vacancy/GenerateJobDescService', () => {
  const originalModule = jest.requireActual('../src/services/job_vacancy/GenerateJobDescService');

  return class GenerateJobDescService extends originalModule {
    async generateJobDesc(_name, _topUserIds, _jobLevelName) {
      return {
        jobTitle: 'Software Engineer',
        jobDescription: {
          key_responsibilities: ['Develop and maintain software applications'],
          qualifications: ["Bachelor's degree in Computer Science"],
          competencies: ['JavaScript', 'Node.js'],
          sucess_metrics: ['Deliver projects on time'],
        },
        onetsocCodes: ['15-1132.00'],
      };
    }
  };
});

// Mock job vacancy service to avoid external API calls
jest.mock('../src/services/JobVacancyService', () => {
  const originalModule = jest.requireActual('../src/services/JobVacancyService');

  return class JobVacancyService extends originalModule {
    async setJobDesc(_data) {
      return;
    }

    async setVacancyGroupVariables(_data) {
      return;
    }
  };
});

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // Uncomment to suppress console.log in tests
  // log: jest.fn(),
  // info: jest.fn(),
  // warn: jest.fn(),
  // error: jest.fn(),
};

// Global test timeout
jest.setTimeout(3000);

// beforeEach(async () => {
//   // CLS (Continuation-Local Storage) is used to track the transaction
//   // across asynchronous operations. We start a new "context" for each test.
//   await namespace.runPromise(async () => {
//     const transaction = await sequelize.transaction();
//     namespace.set('transaction', transaction);
//   });
// });

// Clean up after each test
afterEach(async () => {
  jest.clearAllMocks();

  // // Retrieve the transaction from the CLS
  // const transaction = namespace.get('transaction');
  // if (transaction) {
  //   // Rollback the transaction to undo all changes made during the test
  //   await transaction.rollback();
  // }
});

beforeAll(async () => {
  // Ensure database connection is available
  try {
    await sequelize.authenticate();
  } catch (error) {
    console.error('Failed to connect to test database:', error);
    throw error;
  }
});

afterAll(async () => {
  // Close database connection after all tests
  try {
    await sequelize.close();
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
});
